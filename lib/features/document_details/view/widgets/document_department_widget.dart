import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';
import 'package:paperless_mobile/features/document_upload/cubit/document_upload_cubit.dart';

class DocumentDepartmentWidget extends StatefulWidget {
  const DocumentDepartmentWidget({super.key, required this.documentDetail});

  final DocumentModel documentDetail;

  @override
  State<DocumentDepartmentWidget> createState() =>
      _DocumentDepartmentWidgetState();
}

class _DocumentDepartmentWidgetState extends State<DocumentDepartmentWidget> {
  List<String> department = [];
  late DocumentUploadCubit documentUploadCubit;

  @override
  void initState() {
    super.initState();
    documentUploadCubit = context.read<DocumentUploadCubit>();
    initData();
  }

  void initData() {
    for (int i = 0; i < widget.documentDetail.departments.length; i++) {
      for (int j = 0; j < documentUploadCubit.state.departments.length; j++) {
        if (widget.documentDetail.projects[i] ==
            documentUploadCubit.state.projects[j].id) {
          department.add(documentUploadCubit.state.projects[j].name);
        }
      }
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Departments',
              style: AppTextStyles.textStyle12.copyWith(
                  fontWeight: FontWeight.w500, color: AppColor.grey_909090)),
          const Gap(6),
          Text(department.join(', ') ?? 'None'),
        ],
      ),
    );
  }
}
